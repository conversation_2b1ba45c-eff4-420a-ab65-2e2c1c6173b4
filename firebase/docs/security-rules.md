# Firestore Security Rules Documentation

This document explains the Firestore security rules used in the project.

## Rule Files

- `firestore.rules` - Security rules used across all environments (development, staging, production)

**Best Practice**: Using the same security rules across all environments ensures consistency and prevents security issues that could arise from having different rules in different environments.

## Collections and Access Patterns

### Tublists (`/tublists/{tublistId}`)

**Purpose**: Both public and private tublists in a unified collection

**Production Rules**:
```javascript
match /tublists/{tublistId} {
  allow read: if request.auth != null;
  allow write: if request.auth != null &&
    (resource == null || resource.data.userId == request.auth.uid);
}
```

**Access Control**:
- ✅ All authenticated users can read any tublist (public or private)
- ✅ Users can only write to their own tublists
- ✅ Users can create new tublists
- ❌ Anonymous users cannot access
- ❌ Users cannot modify other users' tublists

**Note**: Privacy is enforced at the application level - private tublists are filtered out in queries, but the security rules allow reading for simplicity and performance.

### User Profiles (`/users/{userId}`)

**Purpose**: User profile information and settings

**Production Rules**:
```javascript
match /users/{userId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
```

**Access Control**:
- ✅ Users can read/write their own profile
- ❌ Users cannot access other users' profiles
- ❌ Anonymous users cannot access

### App Configuration (`/app_config/{configId}`)

**Purpose**: Application-wide configuration (passwords, settings)

**Production Rules**:
```javascript
match /app_config/{configId} {
  allow read: if true; // Allow read for server-side verification (passwords are hashed)
  allow write: if request.auth != null; // Only authenticated users can write
}
```

**Access Control**:
- ✅ Anyone can read (passwords are hashed for security)
- ✅ Authenticated users can write configuration
- ❌ Anonymous users cannot write

## Consistent Security Across Environments

The same security rules are used across all environments (development, staging, production) to ensure:

- **Consistency**: No surprises when moving from development to production
- **Security**: No accidentally permissive rules in production
- **Testing**: Development testing reflects real production security constraints
- **Simplicity**: One set of rules to maintain and understand

If you need to test with different data or permissions during development, use test users with appropriate roles rather than changing the security rules.

## Security Considerations

### Authentication Requirements
- All collections require authentication except for reading app configuration
- User identity is verified using `request.auth.uid`
- Email-based admin access only in development

### Data Ownership
- Users can only access their own data (tublists, profiles)
- Ownership is enforced through `userId` field matching
- Public tublists are readable by all authenticated users

### Password Security
- App passwords are hashed before storage
- Reading hashed passwords is safe for verification
- Only authenticated users can update passwords



## Testing Security Rules

### Using Firebase Emulator
```bash
# Start emulators with rules
npm run firebase:emulators

# Test rules in emulator UI
# Navigate to http://localhost:4000
```

### Rule Testing Commands
```bash
# Test specific rules
firebase emulators:exec --only firestore "npm test"

# Validate rule syntax
firebase firestore:rules:validate firebase/config/firestore.rules
```

### Common Test Scenarios

1. **Unauthenticated Access**
   - Should fail for all collections except app_config reads
   
2. **Cross-User Access**
   - User A should not access User B's personal queues or profile
   
3. **Queue Ownership**
   - Users should only modify queues they created
   
4. **Admin Access (Development)**
   - Company email users should have full access in development

## Rule Deployment

### Deploy Rules Only
```bash
# Deploy to development
npm run firebase:deploy:rules:dev

# Deploy to production
npm run firebase:deploy:rules:prod
```

### Validate Before Deployment
```bash
# Validate rule syntax
firebase firestore:rules:validate firebase/config/firestore.rules

# Test with emulator
firebase emulators:start --only firestore
```

## Troubleshooting Rules

### Common Issues

1. **Permission Denied Errors**
   - Check user authentication status
   - Verify userId matches request.auth.uid
   - Ensure required fields exist in document

2. **Rule Syntax Errors**
   - Validate rules before deployment
   - Check for typos in field names
   - Verify proper JavaScript syntax

3. **Performance Issues**
   - Avoid complex rule logic
   - Use indexes for rule-based queries
   - Minimize rule evaluation complexity

### Debugging Tips

1. **Use Firebase Console**
   - Check rule evaluation logs
   - Monitor failed requests
   - Review security rule metrics

2. **Test with Emulator**
   - Use emulator UI for rule testing
   - Check rule evaluation results
   - Test different user scenarios

3. **Add Logging (Development)**
   - Use debug statements in development rules
   - Log rule evaluation context
   - Monitor authentication state

## Best Practices

1. **Principle of Least Privilege**
   - Grant minimum necessary permissions
   - Restrict access by default
   - Use specific field-level rules when possible

2. **Validate Input Data**
   - Check required fields exist
   - Validate data types and formats
   - Prevent malicious data injection

3. **Environment Separation**
   - Use different rules for development/production
   - Test rules thoroughly before production deployment
   - Keep development rules more permissive for testing

4. **Regular Security Reviews**
   - Review rules periodically
   - Update rules when data model changes
   - Monitor for security vulnerabilities

5. **Documentation**
   - Document rule logic and reasoning
   - Explain complex rule conditions
   - Keep security documentation updated
