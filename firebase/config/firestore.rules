rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Tublists collection - handles both public and private tublists
    match /tublists/{tublistId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (resource == null || resource.data.userId == request.auth.uid);
    }

    // User profiles - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // App configuration - allow read access for server-side API routes
    // Passwords are now hashed, so reading them is safe for verification
    match /app_config/{configId} {
      allow read: if true; // Allow read for server-side verification (passwords are hashed)
      allow write: if request.auth != null; // Only authenticated users can write
    }
  }
}
