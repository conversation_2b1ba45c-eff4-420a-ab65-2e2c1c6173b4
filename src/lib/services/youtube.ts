// YouTube API service for video search and metadata

import {
  YouTubeSearchResponse,
  YouTubeVideoDetailsResponse,
  VideoSearchResult,
  VideoMetadata,
  PaginatedSearchResult
} from '@/lib/types/video'
import { parseYouTubeDuration, getYouTubeThumbnail } from '@/lib/utils/format'

const YOUTUBE_API_BASE = 'https://www.googleapis.com/youtube/v3'

// Rate limiting and retry configuration
const RATE_LIMIT_CONFIG = {
  maxRequestsPerMinute: 100, // Conservative limit to prevent quota exhaustion
  retryAttempts: 3,
  baseDelayMs: 1000, // Base delay for exponential backoff
  maxDelayMs: 10000, // Maximum delay between retries
}

interface RequestQueue {
  timestamp: number
  resolve: (value: any) => void
  reject: (error: any) => void
  request: () => Promise<any>
}

export class YouTubeService {
  private apiKey: string
  private requestQueue: RequestQueue[] = []
  private requestTimestamps: number[] = []
  private isProcessingQueue = false

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY || ''
    if (!this.apiKey || this.apiKey === 'your_youtube_api_key_here') {
      console.log('🔧 YouTube API key not configured')
      console.log('📝 Add NEXT_PUBLIC_YOUTUBE_API_KEY to .env.local to enable video search')
    }
  }

  /**
   * Check if we can make a request based on rate limiting
   */
  private canMakeRequest(): boolean {
    const now = Date.now()
    const oneMinuteAgo = now - 60000

    // Remove timestamps older than 1 minute
    this.requestTimestamps = this.requestTimestamps.filter(timestamp => timestamp > oneMinuteAgo)

    return this.requestTimestamps.length < RATE_LIMIT_CONFIG.maxRequestsPerMinute
  }

  /**
   * Add request timestamp for rate limiting
   */
  private recordRequest(): void {
    this.requestTimestamps.push(Date.now())
  }

  /**
   * Execute request with exponential backoff retry
   */
  private async executeWithRetry<T>(request: () => Promise<T>, context: string): Promise<T> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= RATE_LIMIT_CONFIG.retryAttempts; attempt++) {
      try {
        const result = await request()
        if (attempt > 1) {
          console.log(`✅ ${context} succeeded on attempt ${attempt}`)
        }
        return result
      } catch (error: any) {
        lastError = error
        console.warn(`⚠️ ${context} failed on attempt ${attempt}:`, error.message)

        // Don't retry on certain errors
        if (error.message.includes('401') || error.message.includes('403')) {
          console.error(`🚨 ${context} - API key issue, not retrying:`, error.message)
          throw error
        }

        // Don't retry on the last attempt
        if (attempt === RATE_LIMIT_CONFIG.retryAttempts) {
          break
        }

        // Calculate exponential backoff delay
        const delay = Math.min(
          RATE_LIMIT_CONFIG.baseDelayMs * Math.pow(2, attempt - 1),
          RATE_LIMIT_CONFIG.maxDelayMs
        )

        console.log(`⏳ ${context} - Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError || new Error(`${context} failed after ${RATE_LIMIT_CONFIG.retryAttempts} attempts`)
  }

  /**
   * Queue and execute API request with rate limiting
   */
  private async queueRequest<T>(request: () => Promise<T>, context: string): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        timestamp: Date.now(),
        resolve,
        reject,
        request: () => this.executeWithRetry(request, context)
      })

      this.processQueue()
    })
  }

  /**
   * Process the request queue with rate limiting
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue) {
      return
    }

    this.isProcessingQueue = true

    while (this.requestQueue.length > 0) {
      if (!this.canMakeRequest()) {
        // Wait until we can make another request
        const oldestTimestamp = Math.min(...this.requestTimestamps)
        const waitTime = Math.max(0, 60000 - (Date.now() - oldestTimestamp))

        if (waitTime > 0) {
          console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`)
          await new Promise(resolve => setTimeout(resolve, waitTime))
        }
        continue
      }

      const queueItem = this.requestQueue.shift()
      if (!queueItem) continue

      this.recordRequest()

      try {
        const result = await queueItem.request()
        queueItem.resolve(result)
      } catch (error) {
        queueItem.reject(error)
      }
    }

    this.isProcessingQueue = false
  }

  /**
   * Search for YouTube videos
   */
  async searchVideos(query: string, maxResults?: number): Promise<VideoSearchResult[]>
  async searchVideos(query: string, maxResults?: number, pageToken?: string): Promise<PaginatedSearchResult>
  async searchVideos(query: string, maxResults: number = 20, pageToken?: string): Promise<VideoSearchResult[] | PaginatedSearchResult> {
    if (!this.apiKey || this.apiKey === 'your_youtube_api_key_here') {
      throw new Error('YouTube API key not configured. Please add NEXT_PUBLIC_YOUTUBE_API_KEY to your .env.local file.')
    }

    const searchRequest = async () => {
      const searchUrl = new URL(`${YOUTUBE_API_BASE}/search`)
      searchUrl.searchParams.set('part', 'snippet')
      searchUrl.searchParams.set('q', query)
      searchUrl.searchParams.set('type', 'video')
      searchUrl.searchParams.set('maxResults', maxResults.toString())
      searchUrl.searchParams.set('key', this.apiKey)
      searchUrl.searchParams.set('videoEmbeddable', 'true')
      searchUrl.searchParams.set('videoSyndicated', 'true')

      // Add page token for pagination
      if (pageToken) {
        searchUrl.searchParams.set('pageToken', pageToken)
      }

      const response = await fetch(searchUrl.toString())

      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status} ${response.statusText}`)
      }

      return response.json()
    }

    try {
      const data: YouTubeSearchResponse = await this.queueRequest(
        searchRequest,
        `YouTube search: "${query}"`
      )

      // Get video IDs for detailed information
      const videoIds = data.items.map(item => item.id.videoId).join(',')
      const videoDetails = await this.getVideoDetails(videoIds)

      // Combine search results with detailed information
      const results = data.items.map(item => {
        const details = videoDetails.find(detail => detail.id === item.id.videoId)

        return {
          id: item.id.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnail: {
            url: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default.url,
            width: item.snippet.thumbnails.medium?.width || item.snippet.thumbnails.default.width,
            height: item.snippet.thumbnails.medium?.height || item.snippet.thumbnails.default.height,
          },
          channel: {
            title: item.snippet.channelTitle,
            id: item.snippet.channelId,
          },
          duration: details?.contentDetails.duration || 'PT0S',
          publishedAt: item.snippet.publishedAt,
          viewCount: details?.statistics.viewCount ? parseInt(details.statistics.viewCount) : undefined,
        }
      })

      // Return paginated result if pageToken was provided, otherwise return just the results array
      if (pageToken !== undefined) {
        return {
          results,
          nextPageToken: data.nextPageToken,
          prevPageToken: data.prevPageToken,
          totalResults: data.pageInfo.totalResults,
          currentPage: pageToken ? 2 : 1, // Simple page calculation
          hasNextPage: !!data.nextPageToken,
          hasPrevPage: !!data.prevPageToken
        }
      }

      return results
    } catch (error) {
      console.error('YouTube search error:', error)
      throw error
    }
  }

  /**
   * Search for YouTube videos with pagination support
   */
  async searchVideosPaginated(query: string, maxResults: number = 20, pageToken?: string): Promise<PaginatedSearchResult> {
    const result = await this.searchVideos(query, maxResults, pageToken || '')
    return result as PaginatedSearchResult
  }

  /**
   * Get detailed video information
   */
  async getVideoDetails(videoIds: string): Promise<YouTubeVideoDetailsResponse['items']> {
    if (!this.apiKey) {
      throw new Error('YouTube API key not configured')
    }

    const detailsRequest = async () => {
      const detailsUrl = new URL(`${YOUTUBE_API_BASE}/videos`)
      // Request all necessary fields including snippet for metadata
      detailsUrl.searchParams.set('part', 'snippet,contentDetails,statistics')
      detailsUrl.searchParams.set('id', videoIds)
      detailsUrl.searchParams.set('key', this.apiKey)

      const response = await fetch(detailsUrl.toString())

      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status} ${response.statusText}`)
      }

      return response.json()
    }

    try {
      const data: YouTubeVideoDetailsResponse = await this.queueRequest(
        detailsRequest,
        `YouTube video details: ${videoIds.split(',').length} videos`
      )
      return data.items
    } catch (error) {
      console.error('YouTube video details error:', error)
      throw error
    }
  }

  /**
   * Convert search result to VideoMetadata
   */
  searchResultToVideoMetadata(result: VideoSearchResult): VideoMetadata {
    return {
      id: result.id,
      title: result.title,
      thumbnail: result.thumbnail.url,
      duration: parseYouTubeDuration(result.duration),
      channel: result.channel.title,
      description: result.description,
      publishedAt: result.publishedAt,
      viewCount: result.viewCount,
      url: `https://www.youtube.com/watch?v=${result.id}`,
    }
  }

  /**
   * Get video metadata by ID
   */
  async getVideoMetadata(videoId: string): Promise<VideoMetadata | null> {
    try {
      const details = await this.getVideoDetails(videoId)
      const video = details[0]
      
      if (!video) {
        return null
      }

      return {
        id: video.id,
        title: video.snippet.title,
        thumbnail: getYouTubeThumbnail(video.id, 'medium'),
        duration: parseYouTubeDuration(video.contentDetails.duration),
        channel: video.snippet.channelTitle,
        description: video.snippet.description,
        publishedAt: video.snippet.publishedAt,
        viewCount: video.statistics.viewCount ? parseInt(video.statistics.viewCount) : undefined,
        url: `https://www.youtube.com/watch?v=${video.id}`,
      }
    } catch (error) {
      console.error('Error getting video metadata:', error)
      return null
    }
  }

  /**
   * Extract video ID from YouTube URL
   */
  extractVideoId(url: string): string | null {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/v\/([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/,
      /youtube\.com\/shorts\/([^&\n?#]+)/
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match && match[1]) {
        return match[1]
      }
    }

    return null
  }

  /**
   * Validate YouTube URL
   */
  isValidYouTubeUrl(url: string): boolean {
    return this.extractVideoId(url) !== null
  }


}

// Export singleton instance
export const youtubeService = new YouTubeService()
