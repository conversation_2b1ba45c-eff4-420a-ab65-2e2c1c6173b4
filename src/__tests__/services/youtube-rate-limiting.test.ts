// Test YouTube API rate limiting and retry logic

import { YouTubeService } from '@/lib/services/youtube'

// Mock fetch for testing
global.fetch = jest.fn()

describe('YouTube API Rate Limiting', () => {
  let youtubeService: YouTubeService
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    // Reset mocks
    mockFetch.mockClear()
    
    // Mock environment variable
    process.env.NEXT_PUBLIC_YOUTUBE_API_KEY = 'test-api-key'
    
    // Create new instance for each test
    youtubeService = new (YouTubeService as any)()
  })

  afterEach(() => {
    jest.clearAllTimers()
    jest.useRealTimers()
  })

  describe('Rate Limiting', () => {
    it('should queue requests when rate limit is reached', async () => {
      jest.useFakeTimers()
      
      // Mock successful responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          items: [],
          pageInfo: { totalResults: 0 }
        })
      } as Response)

      // Make multiple requests quickly
      const promises = Array.from({ length: 5 }, (_, i) => 
        youtubeService.searchVideos(`test query ${i}`, 1)
      )

      // Fast-forward timers to process queue
      jest.advanceTimersByTime(1000)
      
      await Promise.all(promises)

      // Should have made requests (exact count depends on rate limiting)
      expect(mockFetch).toHaveBeenCalled()
      
      jest.useRealTimers()
    })
  })

  describe('Retry Logic', () => {
    it('should retry on 500 errors with exponential backoff', async () => {
      jest.useFakeTimers()

      // Mock 500 error then success
      mockFetch
        .mockRejectedValueOnce(new Error('YouTube API error: 500 Internal Server Error'))
        .mockRejectedValueOnce(new Error('YouTube API error: 500 Internal Server Error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            items: [],
            pageInfo: { totalResults: 0 }
          })
        } as Response)

      const searchPromise = youtubeService.searchVideos('test query', 1)
      
      // Fast-forward through retry delays
      jest.advanceTimersByTime(10000)
      
      await expect(searchPromise).resolves.toBeDefined()
      expect(mockFetch).toHaveBeenCalledTimes(3) // Initial + 2 retries
      
      jest.useRealTimers()
    })

    it('should not retry on 403 errors', async () => {
      mockFetch.mockRejectedValue(new Error('YouTube API error: 403 Forbidden'))

      await expect(youtubeService.searchVideos('test query', 1))
        .rejects.toThrow('403 Forbidden')
      
      expect(mockFetch).toHaveBeenCalledTimes(1) // No retries
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(youtubeService.searchVideos('test query', 1))
        .rejects.toThrow('failed after 3 attempts')
    })

    it('should handle malformed responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      } as Response)

      await expect(youtubeService.searchVideos('test query', 1))
        .rejects.toThrow('Invalid JSON')
    })
  })
})
