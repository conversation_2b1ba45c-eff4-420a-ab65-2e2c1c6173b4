/**
 * @jest-environment jsdom
 */

import { renderHook, act, waitFor } from '@testing-library/react'
import { useScrollToBottom } from '@/hooks/useScrollToBottom'

// Mock window properties
const mockScrollTo = jest.fn()
Object.defineProperty(window, 'scrollTo', { value: mockScrollTo })

describe('useScrollToBottom', () => {
  beforeEach(() => {
    // Reset DOM dimensions
    Object.defineProperty(window, 'pageYOffset', { value: 0, writable: true })
    Object.defineProperty(window, 'innerHeight', { value: 800, writable: true })
    Object.defineProperty(document.documentElement, 'scrollTop', { value: 0, writable: true })
    Object.defineProperty(document.documentElement, 'scrollHeight', { value: 2000, writable: true })
    
    jest.clearAllMocks()
  })

  it('should return false when not near bottom', () => {
    const { result } = renderHook(() => useScrollToBottom({ threshold: 200 }))
    expect(result.current).toBe(false)
  })

  it('should return true when near bottom', () => {
    // Set scroll position near bottom
    Object.defineProperty(window, 'pageYOffset', { value: 1000, writable: true })
    Object.defineProperty(document.documentElement, 'scrollTop', { value: 1000, writable: true })
    
    const { result } = renderHook(() => useScrollToBottom({ threshold: 200 }))
    expect(result.current).toBe(true)
  })

  it('should handle scroll events', async () => {
    const { result } = renderHook(() => useScrollToBottom({ threshold: 200, debounceMs: 10 }))

    expect(result.current).toBe(false)

    // Simulate scroll to bottom
    act(() => {
      Object.defineProperty(window, 'pageYOffset', { value: 1000, writable: true })
      Object.defineProperty(document.documentElement, 'scrollTop', { value: 1000, writable: true })

      // Trigger scroll event
      const scrollEvent = new Event('scroll')
      window.dispatchEvent(scrollEvent)
    })

    // Wait for debounced function to execute
    await waitFor(() => {
      expect(result.current).toBe(true)
    })
  })

  it('should handle resize events', () => {
    const { result } = renderHook(() => useScrollToBottom({ threshold: 200 }))

    // Just verify that resize event listener is added without errors
    act(() => {
      const resizeEvent = new Event('resize')
      window.dispatchEvent(resizeEvent)
    })

    // Should not throw errors
    expect(result.current).toBeDefined()
  })

  it('should use custom threshold', () => {
    Object.defineProperty(window, 'pageYOffset', { value: 900, writable: true })
    Object.defineProperty(document.documentElement, 'scrollTop', { value: 900, writable: true })
    
    const { result: result100 } = renderHook(() => useScrollToBottom({ threshold: 100 }))
    const { result: result300 } = renderHook(() => useScrollToBottom({ threshold: 300 }))
    
    expect(result100.current).toBe(false) // 300px from bottom, threshold 100
    expect(result300.current).toBe(true)  // 300px from bottom, threshold 300
  })
})
