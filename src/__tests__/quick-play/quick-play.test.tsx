/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { InstantTubeProvider } from '@/components/providers/InstantTubeProvider'
import { QuickPlayUrlInput } from '@/components/instant-tube/QuickPlayUrlInput'
import { useQuickPlay } from '@/hooks/useInstantTube'

// Mock the YouTube service
jest.mock('@/lib/services/youtube', () => ({
  youtubeService: {
    isValidYouTubeUrl: jest.fn((url: string) => {
      return url.includes('youtube.com') || url.includes('youtu.be')
    }),
    extractVideoId: jest.fn((url: string) => {
      if (url.includes('watch?v=')) {
        return url.split('watch?v=')[1].split('&')[0]
      }
      if (url.includes('youtu.be/')) {
        return url.split('youtu.be/')[1].split('?')[0]
      }
      return null
    }),
    getVideoMetadata: jest.fn().mockResolvedValue({
      id: 'dQw4w9WgXcQ',
      title: 'Test Video',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
      duration: 212,
      channel: 'Test Channel',
      description: 'Test Description',
      publishedAt: '2023-01-01T00:00:00Z',
      viewCount: 1000000,
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    })
  }
}))

// Mock the useI18n hook
jest.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'quickPlay.title': 'Quick Play',
        'quickPlay.description': 'Instantly play and loop any YouTube video without saving to queues',
        'quickPlay.urlPlaceholder': 'Paste YouTube URL here (e.g., https://youtube.com/watch?v=...)',
        'quickPlay.playButton': 'Play',
        'quickPlay.noVideoLoaded': 'No video loaded',
        'quickPlay.enterYouTubeUrl': 'Enter a YouTube URL above to start playing',
        'quickPlay.enableLoop': 'Enable infinite loop',
        'quickPlay.disableLoop': 'Disable loop',
        'quickPlay.clearVideo': 'Clear video',
        'quickPlay.featuresTitle': 'Why use Quick Play?',
        'quickPlay.feature1': 'No queue management needed',
        'quickPlay.feature2': 'Instant YouTube URL playback',
        'quickPlay.feature3': 'Simple infinite loop control',
        'quickPlay.feature4': 'Perfect for single video loops',
        'quickPlay.supportedFormats': 'Supported formats:',
        'common.loading': 'Loading...',
        'common.clear': 'Clear',
        'controls.play': 'Play',
        'controls.pause': 'Pause'
      }
      return translations[key] || key
    }
  })
}))

// Test component to access quick play state
const TestComponent = () => {
  const { currentVideo, isLoading, error, loadVideo, clearVideo } = useQuickPlay()

  return (
    <div>
      <div data-testid="current-video">{currentVideo?.title || 'No video'}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{error || 'No error'}</div>
      <button onClick={() => loadVideo('https://www.youtube.com/watch?v=dQw4w9WgXcQ')}>
        Load Video
      </button>
      <button onClick={clearVideo}>Clear Video</button>
    </div>
  )
}

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <InstantTubeProvider>
    {children}
  </InstantTubeProvider>
)

describe('Quick Play Feature', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders URL input component', () => {
    render(
      <TestWrapper>
        <QuickPlayUrlInput />
      </TestWrapper>
    )

    expect(screen.getByText('Quick Play')).toBeInTheDocument()
    expect(screen.getByText('Instantly play and loop any YouTube video without saving to queues')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/Paste YouTube URL here/)).toBeInTheDocument()
  })

  test('validates YouTube URLs correctly', () => {
    render(
      <TestWrapper>
        <QuickPlayUrlInput />
      </TestWrapper>
    )

    const input = screen.getByPlaceholderText(/Paste YouTube URL here/)
    const playButton = screen.getByRole('button', { name: /Play/i })

    // Initially disabled
    expect(playButton).toBeDisabled()

    // Invalid URL
    fireEvent.change(input, { target: { value: 'not-a-url' } })
    expect(playButton).toBeDisabled()

    // Valid YouTube URL
    fireEvent.change(input, { target: { value: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' } })
    expect(playButton).not.toBeDisabled()
  })

  test('loads video when valid URL is submitted', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    )

    const loadButton = screen.getByText('Load Video')

    // Initially no video
    expect(screen.getByTestId('current-video')).toHaveTextContent('No video')
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')

    // Click load video
    fireEvent.click(loadButton)

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Loading')
    })

    // Should eventually load the video
    await waitFor(() => {
      expect(screen.getByTestId('current-video')).toHaveTextContent('Test Video')
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')
    }, { timeout: 3000 })
  })

  test('clears video when clear button is clicked', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    )

    const loadButton = screen.getByText('Load Video')
    const clearButton = screen.getByText('Clear Video')

    // Load a video first
    fireEvent.click(loadButton)

    // Wait for video to load
    await waitFor(() => {
      expect(screen.getByTestId('current-video')).toHaveTextContent('Test Video')
    }, { timeout: 3000 })

    // Click clear button
    fireEvent.click(clearButton)

    // Should return to initial state
    await waitFor(() => {
      expect(screen.getByTestId('current-video')).toHaveTextContent('No video')
    })
  })

  test('provider manages state correctly', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    )

    // Should start with initial state
    expect(screen.getByTestId('current-video')).toHaveTextContent('No video')
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading')
    expect(screen.getByTestId('error')).toHaveTextContent('No error')
  })
})
