'use client'

import { useQueue } from '@/hooks/useTublist'
import { useVideoPlayer } from '@/hooks/useVideoPlayer'
import { formatTime } from '@/lib/utils/time'

export function TimeframeDisplay() {
  const { currentVideo } = useQueue()
  const { currentTimeframeIndex, currentTime, isInTimeframeMode } = useVideoPlayer()

  if (!currentVideo || !currentVideo.timeframes || currentVideo.timeframes.length === 0) {
    return null
  }

  const timeframes = currentVideo.timeframes
  const loopMode = currentVideo.loopSettings.loopMode

  return (
    <div className="glassmorphism rounded-xl p-4 mt-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-white flex items-center gap-2">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          Timeframes ({timeframes.length})
        </h3>
        <div className="flex items-center gap-2">
          <span className="text-xs text-dark-400">Mode:</span>
          <span className={`text-xs px-2 py-0.5 rounded ${
            loopMode === 'timeframes-only' 
              ? 'bg-primary-600/20 text-primary-300' 
              : 'bg-blue-600/20 text-blue-300'
          }`}>
            {loopMode === 'timeframes-only' ? 'Timeframes Only' : 'Full Video + Timeframes'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
        {timeframes.map((timeframe, index) => (
          <TimeframeItem
            key={timeframe.id}
            timeframe={timeframe}
            index={index}
            isActive={isInTimeframeMode && index === currentTimeframeIndex}
            currentTime={currentTime}
          />
        ))}
      </div>
    </div>
  )
}

interface TimeframeItemProps {
  timeframe: {
    id: string
    startTime: number
    endTime: number
    loopCount: number
  }
  index: number
  isActive: boolean
  currentTime: number
}

function TimeframeItem({ timeframe, index, isActive, currentTime }: TimeframeItemProps) {
  const duration = timeframe.endTime - timeframe.startTime

  // Calculate progress within the timeframe
  const progress = isActive && currentTime >= timeframe.startTime && currentTime <= timeframe.endTime
    ? ((currentTime - timeframe.startTime) / duration) * 100
    : 0

  return (
    <div className={`p-3 rounded-lg border transition-all ${
      isActive 
        ? 'bg-primary-600/20 border-primary-500/50 shadow-lg shadow-primary-500/20' 
        : 'bg-dark-700/50 border-dark-600/50 hover:bg-dark-600/50'
    }`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
            isActive 
              ? 'bg-primary-500 text-white' 
              : 'bg-dark-600 text-dark-300'
          }`}>
            {index + 1}
          </span>
          {isActive && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-primary-400 font-medium">Playing</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-1">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
          <span className="text-xs text-dark-300">{timeframe.loopCount}x</span>
        </div>
      </div>

      <div className="space-y-1">
        <div className="flex items-center justify-between text-xs">
          <span className="text-dark-400">Start:</span>
          <span className="text-white font-mono">{formatTime(timeframe.startTime)}</span>
        </div>
        <div className="flex items-center justify-between text-xs">
          <span className="text-dark-400">End:</span>
          <span className="text-white font-mono">{formatTime(timeframe.endTime)}</span>
        </div>
        <div className="flex items-center justify-between text-xs">
          <span className="text-dark-400">Duration:</span>
          <span className="text-primary-300 font-mono">{formatTime(duration)}</span>
        </div>
      </div>

      {/* Progress bar for active timeframe */}
      {isActive && (
        <div className="mt-2">
          <div className="w-full bg-dark-600 rounded-full h-1">
            <div
              className="bg-primary-500 h-1 rounded-full transition-all duration-300"
              style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
            ></div>
          </div>
          <div className="text-xs text-primary-400 mt-1 text-center">
            {formatTime(currentTime)} / {formatTime(timeframe.endTime)}
          </div>
        </div>
      )}
    </div>
  )
}
