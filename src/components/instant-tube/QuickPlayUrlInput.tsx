'use client'

import { useState, useRef } from 'react'
import { useQuickPlay } from '@/hooks/useInstantTube'
import { useI18n } from '@/hooks/useI18n'
import { youtubeService } from '@/lib/services/youtube'

export function QuickPlayUrlInput() {
  const [url, setUrl] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const { loadVideo, isLoading, error } = useQuickPlay()
  const { t } = useI18n()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const trimmedUrl = url.trim()
    if (!trimmedUrl) return

    await loadVideo(trimmedUrl)
  }

  const handleClear = () => {
    setUrl('')
    inputRef.current?.focus()
  }

  const isValidUrl = url.trim() && youtubeService.isValidYouTubeUrl(url.trim())

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">
          {t('quickPlay.title')}
        </h2>
        <p className="text-dark-300">
          {t('quickPlay.description')}
        </p>
      </div>

      {/* URL Input Form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder={t('quickPlay.urlPlaceholder')}
            disabled={isLoading}
            className="input-field pr-24 pl-12"
          />
          
          {/* YouTube Icon */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-dark-400">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
          </div>

          {/* Action Buttons */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {url && (
              <button
                type="button"
                onClick={handleClear}
                disabled={isLoading}
                className="p-1.5 rounded-lg text-dark-400 hover:text-white hover:bg-white/10 transition-colors duration-200 disabled:opacity-30"
                title={t('common.clear')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            )}
            
            <button
              type="submit"
              disabled={!isValidUrl || isLoading}
              className="px-4 py-1.5 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors duration-200 text-sm font-medium"
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>{t('common.loading')}</span>
                </div>
              ) : (
                t('quickPlay.playButton')
              )}
            </button>
          </div>
        </div>
      </form>

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-red-400 flex-shrink-0">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-center text-xs text-dark-400 space-y-1">
        <p>{t('quickPlay.supportedFormats')}</p>
        <div className="flex justify-center space-x-4 text-dark-500">
          <span>youtube.com/watch?v=...</span>
          <span>youtu.be/...</span>
          <span>youtube.com/shorts/...</span>
        </div>
      </div>
    </div>
  )
}
