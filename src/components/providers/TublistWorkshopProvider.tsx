'use client'

import { ReactNode, useState, useEffect, useCallback } from 'react'
import { DraftQueueContext } from '@/hooks/useTublistWorkshop'
import { VideoMetadata, DraftVideoItem } from '@/lib/types/video'
import { Queue, QueueItem } from '@/lib/types/queue'
import { useAuth } from '@/hooks/useAuth'
import { firebaseService } from '@/lib/services/firebase'
import { getTotalDraftDurationNew } from '@/lib/utils/time'

interface DraftQueueProviderProps {
  children: ReactNode
}

const DRAFT_QUEUE_STORAGE_KEY = 'draftQueue'

export function TublistWorkshopProvider({ children }: DraftQueueProviderProps) {
  const { user } = useAuth()
  const [draftItems, setDraftItems] = useState<DraftVideoItem[]>([])
  const [isCreationMode, setIsCreationMode] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingQueueId, setEditingQueueId] = useState<string | null>(null)
  const [originalQueueMetadata, setOriginalQueueMetadata] = useState<import('@/lib/types/queue').QueueMetadata | null>(null)

  // Load draft queue from localStorage on mount
  useEffect(() => {
    loadDraftFromStorage()
  }, [])

  const saveDraftToStorage = useCallback(() => {
    try {
      localStorage.setItem(DRAFT_QUEUE_STORAGE_KEY, JSON.stringify(draftItems))
    } catch (error) {
      console.warn('Could not save draft queue to storage:', error)
    }
  }, [draftItems])

  // Save draft queue to localStorage whenever it changes
  useEffect(() => {
    saveDraftToStorage()
  }, [saveDraftToStorage])

  const loadDraftFromStorage = () => {
    try {
      const saved = localStorage.getItem(DRAFT_QUEUE_STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        if (Array.isArray(parsed)) {
          setDraftItems(parsed)
          console.log('📂 Loaded draft queue from storage:', parsed.length, 'videos')
        }
      }
    } catch (error) {
      console.warn('Could not load draft queue from storage:', error)
      setDraftItems([])
    }
  }

  const clearDraftFromStorage = () => {
    try {
      localStorage.removeItem(DRAFT_QUEUE_STORAGE_KEY)
    } catch (error) {
      console.warn('Could not clear draft queue from storage:', error)
    }
  }

  const addToDraft = (video: VideoMetadata): string => {
    if (!video || !video.id) {
      console.log('Invalid video object')
      return ''
    }

    // Create a unique draft ID for this item (allows same video multiple times)
    const draftId = `${video.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Create enhanced draft video item with default settings
    const draftVideoItem: DraftVideoItem = {
      ...video,
      draftId,
      timeframes: [], // Start with no timeframes
      loopSettings: {
        videoLoopCount: 1, // Default video loop count
        loopMode: 'whole-video-plus-timeframes' // Default mode
      },
      addedToDraftAt: Date.now()
    }

    // Add video to draft queue (no duplicate prevention - same video can be added multiple times)
    setDraftItems(prev => [...prev, draftVideoItem])
    console.log(`➕ Added to draft queue: ${video.title} (Video Loop: ${draftVideoItem.loopSettings.videoLoopCount}x, ID: ${draftId})`)
    return draftId
  }

  const removeFromDraft = (draftId: string): boolean => {
    const itemIndex = draftItems.findIndex(item => item.draftId === draftId)
    if (itemIndex === -1) {
      console.log('Draft item not found:', draftId)
      return false
    }

    const removedVideo = draftItems[itemIndex]
    setDraftItems(prev => prev.filter(item => item.draftId !== draftId))
    console.log(`➖ Removed from draft queue: ${removedVideo.title} (ID: ${draftId})`)
    return true
  }

  const updateDraftItem = (draftId: string, updates: Partial<Pick<DraftVideoItem, 'timeframes' | 'loopSettings'>>): boolean => {
    const itemIndex = draftItems.findIndex(item => item.draftId === draftId)
    if (itemIndex === -1) {
      console.log('Draft item not found for update:', draftId)
      return false
    }

    setDraftItems(prev => prev.map(item => {
      if (item.draftId === draftId) {
        const updatedItem = { ...item, ...updates }

        // Validate loop settings
        if (updates.loopSettings) {
          updatedItem.loopSettings = {
            ...item.loopSettings,
            ...updates.loopSettings,
            videoLoopCount: Math.max(1, updates.loopSettings.videoLoopCount || item.loopSettings.videoLoopCount)
          }
        }

        console.log(`🔄 Updated draft item: ${item.title} (ID: ${draftId})`, updates)
        return updatedItem
      }
      return item
    }))
    return true
  }

  // Timeframe management functions
  const addTimeframe = (draftId: string, startTime: number, endTime: number, loopCount: number = 1): string => {
    const item = draftItems.find(item => item.draftId === draftId)
    if (!item) {
      console.log('Draft item not found for adding timeframe:', draftId)
      return ''
    }

    // Validate timeframe bounds
    const validStartTime = Math.max(0, Math.min(startTime, item.duration))
    const validEndTime = Math.max(validStartTime + 1, Math.min(endTime, item.duration))

    // Create unique timeframe ID
    const timeframeId = `${draftId}-tf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    const newTimeframe = {
      id: timeframeId,
      startTime: validStartTime,
      endTime: validEndTime,
      loopCount: Math.max(1, loopCount)
    }

    setDraftItems(prev => prev.map(item => {
      if (item.draftId === draftId) {
        return {
          ...item,
          timeframes: [...item.timeframes, newTimeframe]
        }
      }
      return item
    }))

    console.log(`➕ Added timeframe to ${item.title}: ${validStartTime}s-${validEndTime}s (${loopCount} loops)`)
    return timeframeId
  }

  const removeTimeframe = (draftId: string, timeframeId: string): boolean => {
    const item = draftItems.find(item => item.draftId === draftId)
    if (!item) {
      console.log('Draft item not found for removing timeframe:', draftId)
      return false
    }

    setDraftItems(prev => prev.map(item => {
      if (item.draftId === draftId) {
        return {
          ...item,
          timeframes: item.timeframes.filter(tf => tf.id !== timeframeId)
        }
      }
      return item
    }))

    console.log(`➖ Removed timeframe from ${item.title}`)
    return true
  }

  const updateTimeframe = (draftId: string, timeframeId: string, updates: Partial<Pick<import('@/lib/types/video').VideoTimeframe, 'startTime' | 'endTime' | 'loopCount'>>): boolean => {
    const item = draftItems.find(item => item.draftId === draftId)
    if (!item) {
      console.log('Draft item not found for updating timeframe:', draftId)
      return false
    }

    setDraftItems(prev => prev.map(item => {
      if (item.draftId === draftId) {
        return {
          ...item,
          timeframes: item.timeframes.map(tf => {
            if (tf.id === timeframeId) {
              const updatedTimeframe = { ...tf, ...updates }

              // Validate bounds
              if (updates.startTime !== undefined) {
                updatedTimeframe.startTime = Math.max(0, Math.min(updates.startTime, item.duration))
              }
              if (updates.endTime !== undefined) {
                updatedTimeframe.endTime = Math.max(updatedTimeframe.startTime + 1, Math.min(updates.endTime, item.duration))
              }
              if (updates.loopCount !== undefined) {
                updatedTimeframe.loopCount = Math.max(1, updates.loopCount)
              }

              return updatedTimeframe
            }
            return tf
          })
        }
      }
      return item
    }))

    console.log(`🔄 Updated timeframe in ${item.title}`, updates)
    return true
  }

  const getDraftItem = (draftId: string): DraftVideoItem | undefined => {
    return draftItems.find(item => item.draftId === draftId)
  }

  const clearDraft = () => {
    if (draftItems.length === 0) {
      console.log('Draft queue is already empty')
      return
    }

    setDraftItems([])
    console.log('🗑️ Draft queue cleared')
  }

  const isInDraft = (videoId: string): boolean => {
    return draftItems.some(video => video.id === videoId)
  }

  const enterCreationMode = () => {
    setIsCreationMode(true)
    loadDraftFromStorage() // Reload from storage when entering creation mode
    console.log('📝 Entered queue creation mode')
  }

  const exitCreationMode = () => {
    setIsCreationMode(false)
    setDraftItems([])
    clearDraftFromStorage()
    console.log('❌ Exited queue creation mode')
  }

  const enterEditMode = (queue: Queue) => {
    setIsEditMode(true)
    setEditingQueueId(queue.id)
    setOriginalQueueMetadata(queue.metadata)

    // Convert queue items to draft items
    const draftItemsFromQueue: DraftVideoItem[] = queue.queueData.items.map((queueItem: QueueItem) => ({
      id: queueItem.id,
      title: queueItem.title,
      thumbnail: queueItem.thumbnail,
      duration: queueItem.duration,
      channel: queueItem.channel,
      description: queueItem.description,
      publishedAt: queueItem.publishedAt,
      viewCount: queueItem.viewCount,
      url: queueItem.url,
      draftId: `edit-${queueItem.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timeframes: queueItem.timeframes || [],
      loopSettings: queueItem.loopSettings || {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes'
      },
      addedToDraftAt: Date.now()
    }))

    setDraftItems(draftItemsFromQueue)
    console.log('✏️ Entered queue edit mode for queue:', queue.id)
  }

  const exitEditMode = () => {
    setIsEditMode(false)
    setEditingQueueId(null)
    setOriginalQueueMetadata(null)
    setDraftItems([])
    clearDraftFromStorage()
    console.log('❌ Exited queue edit mode')
  }

  const saveDraftAsQueue = async (title: string, isPublic = false, queueLoopCount = -1): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot save queue')
      return null
    }

    if (draftItems.length === 0) {
      console.warn('Cannot save empty draft queue')
      return null
    }

    try {
      // Create a queue state from draft items
      const queueState = {
        items: draftItems.map((draftItem, index) => {
          // Convert DraftVideoItem to QueueItem with new structure
          const queueItem: any = {
            id: draftItem.id,
            title: draftItem.title,
            thumbnail: draftItem.thumbnail,
            duration: draftItem.duration,
            channel: draftItem.channel,
            description: draftItem.description,
            publishedAt: draftItem.publishedAt,
            viewCount: draftItem.viewCount,
            url: draftItem.url,
            addedAt: Date.now(),
            queueIndex: index,
            timeframes: draftItem.timeframes,
            loopSettings: draftItem.loopSettings
          }

          return queueItem
        }),
        currentIndex: 0,
        isPlaying: false,
        queueLoopCount: queueLoopCount,
        volume: 1,
        timestamp: Date.now()
      }

      const queueId = await firebaseService.saveQueue(
        user.uid,
        queueState,
        {
          title,
          description: `Queue with ${draftItems.length} videos`,
        },
        isPublic
      )

      if (queueId) {
        console.log(`✅ Draft queue saved to Firebase ${isPublic ? '(public)' : '(private)'}:`, queueId)
        return queueId
      }

      return null
    } catch (error) {
      console.error('❌ Failed to save draft queue:', error)
      return null
    }
  }

  const updateExistingQueue = async (title: string, description?: string, tags?: string[], queueLoopCount = -1): Promise<boolean> => {
    if (!user || !editingQueueId || !originalQueueMetadata) {
      console.warn('User not authenticated, not in edit mode, or missing original metadata, cannot update queue')
      return false
    }

    if (draftItems.length === 0) {
      console.warn('Cannot save empty queue')
      return false
    }

    try {
      // Create updated queue state from draft items
      const updatedQueueData = {
        items: draftItems.map((draftItem, index) => {
          const queueItem: any = {
            id: draftItem.id,
            title: draftItem.title,
            thumbnail: draftItem.thumbnail,
            duration: draftItem.duration,
            channel: draftItem.channel,
            description: draftItem.description,
            publishedAt: draftItem.publishedAt,
            viewCount: draftItem.viewCount,
            url: draftItem.url,
            addedAt: Date.now(),
            queueIndex: index,
            timeframes: draftItem.timeframes,
            loopSettings: draftItem.loopSettings
          }

          return queueItem
        }),
        currentIndex: 0,
        isPlaying: false,
        queueLoopCount: queueLoopCount,
        volume: 1,
        timestamp: Date.now()
      }

      // Update metadata - preserve existing metadata and only update specific fields
      const updatedMetadata = {
        ...originalQueueMetadata,
        title: title.trim(),
        description: description?.trim() || '',
        tags: tags || [],
        videoCount: draftItems.length,
        totalDuration: draftItems.reduce((total, item) => total + (item.duration || 0), 0),
        firstVideoThumbnail: draftItems[0]?.thumbnail || '',
        lastModified: Date.now(),
      }

      // Save to Firebase using unified update method
      const updateData = {
        metadata: updatedMetadata,
        queueData: updatedQueueData,
      }

      const success = await firebaseService.updateQueue(editingQueueId, updateData, user!.uid)

      if (success) {
        console.log('✅ Queue updated successfully:', editingQueueId)
        return true
      } else {
        console.error('Failed to update queue')
        return false
      }
    } catch (error) {
      console.error('❌ Failed to update queue:', error)
      return false
    }
  }

  // Computed properties
  const draftCount = draftItems.length
  const draftDuration = draftItems.reduce((total, draftItem) => {
    return total + getTotalDraftDurationNew(draftItem)
  }, 0)

  const value = {
    draftItems,
    addToDraft,
    removeFromDraft,
    updateDraftItem,
    clearDraft,
    isInDraft,
    getDraftItem,
    addTimeframe,
    removeTimeframe,
    updateTimeframe,
    draftCount,
    draftDuration,
    isCreationMode,
    enterCreationMode,
    exitCreationMode,
    isEditMode,
    editingQueueId,
    enterEditMode,
    exitEditMode,
    saveDraftAsQueue,
    updateExistingQueue,
  }

  return (
    <DraftQueueContext.Provider value={value}>
      {children}
    </DraftQueueContext.Provider>
  )
}
