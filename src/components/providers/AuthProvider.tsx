'use client'

import { ReactN<PERSON>, useState, useEffect, useCallback } from 'react'
import {
  signInWithPopup,
  linkWithPopup,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInAnonymously as firebaseSignInAnonymously,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  onAuthStateChanged,
  linkWithCredential,
  signInWithCredential,
  EmailAuthProvider,
  deleteUser,
  User as FirebaseUser
} from 'firebase/auth'
import { AuthContext } from '@/hooks/useAuth'
import { User, AUTH_ERROR_CODES } from '@/lib/types/auth'
import { useFirebase } from './FirebaseProvider'
import { firebaseAccountService, FirebaseService } from '@/lib/services'

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreatingUser, setIsCreatingUser] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { auth, db, isInitialized } = useFirebase()

  const isAuthenticated = !!user
  const isUserReady = isAuthenticated && !isCreatingUser

  // Convert Firebase user to our User type
  const convertFirebaseUser = (firebaseUser: FirebaseUser): User => ({
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified,
    isAnonymous: firebaseUser.isAnonymous,
    createdAt: Date.now(),
    lastLoginAt: Date.now(),
  })

  // Helper function to handle data migration with consistent error handling
  const handleDataMigration = async (anonymousUid: string, authenticatedUid: string): Promise<void> => {
    if (!db || anonymousUid === authenticatedUid) return

    try {
      const firebaseService = new FirebaseService()
      await firebaseService.migrateAnonymousUserData(anonymousUid, authenticatedUid)
      console.log('✅ Data migration completed')
    } catch (migrationError: any) {
      if (migrationError.code === 'permission-denied') {
        console.log('ℹ️ No migration needed - anonymous user likely has no data to migrate')
      } else {
        console.log('⚠️ Data migration failed, but continuing with authentication:', migrationError.message)
      }
    }
  }



  const signInWithGoogle = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const provider = new GoogleAuthProvider()
      provider.addScope('email')
      provider.addScope('profile')

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting Google sign-in...')

        // Store the anonymous user ID for potential data migration
        const anonymousUid = currentUser.uid

        // First, try regular sign-in with popup (this handles returning users)
        try {
          console.log('🔄 Trying direct Google sign-in first...')
          const result = await signInWithPopup(auth, provider)
          const signedInUser = result.user

          console.log('✅ Direct Google sign-in successful:', signedInUser.uid)
          console.log('ℹ️ Skipping data migration - signing into existing account')

          const convertedUser = convertFirebaseUser(signedInUser)
          setUser(convertedUser)
          return convertedUser
        } catch (signInError: any) {
          // If direct sign-in fails, try linking (for new Google accounts)
          console.log('🔗 Direct sign-in failed, trying account linking...', signInError.code)

          // Don't try linking if the popup was blocked or user cancelled
          if (signInError.code === 'auth/popup-blocked' ||
              signInError.code === 'auth/popup-closed-by-user' ||
              signInError.code === 'auth/cancelled-popup-request') {
            console.log('❌ Popup blocked or cancelled, not attempting linking')
            throw signInError
          }

          try {
            const result = await linkWithPopup(currentUser, provider)
            const linkedUser = result.user

            console.log('✅ Account linked successfully:', linkedUser.uid)

            // Migrate anonymous user data to the linked account
            console.log('📦 Migrating anonymous user data...')
            await handleDataMigration(anonymousUid, linkedUser.uid)

            const convertedUser = convertFirebaseUser(linkedUser)
            setUser(convertedUser)
            return convertedUser
          } catch (linkError: any) {
          // Handle credential already in use error
          if (linkError.code === AUTH_ERROR_CODES.CREDENTIAL_ALREADY_IN_USE) {
            console.log('🔄 Credential already in use, signing in with existing account...')

            const credential = linkError.credential
            if (credential) {
              try {
                const result = await signInWithCredential(auth, credential)
                const existingUser = result.user

                console.log('✅ Signed in with existing account:', existingUser.uid)
                console.log('ℹ️ Skipping data migration - user already has existing account data')

                const convertedUser = convertFirebaseUser(existingUser)
                setUser(convertedUser)
                return convertedUser
              } catch (signInError: any) {
                console.error('❌ Failed to sign in with credential:', signInError)
                throw linkError
              }
            } else {
              console.warn('⚠️ No credential available in error object')
              throw linkError
            }
          }
          // Re-throw other linking errors
          throw linkError
          }
        }
      } else {
        // Regular Google sign-in for non-anonymous users
        const result = await signInWithPopup(auth, provider)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Google sign in error:', error)
      setError(error.message || 'Failed to sign in with Google')
      return null
    }
  }

  const signInWithEmail = async (email: string, password: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        try {
          // Create email credential and link
          const credential = EmailAuthProvider.credential(email, password)
          const result = await linkWithCredential(currentUser, credential)
          const linkedUser = result.user

          console.log('✅ Account linked successfully:', linkedUser.uid)

          // Migrate anonymous user data to the linked account
          await handleDataMigration(anonymousUid, linkedUser.uid)

          const convertedUser = convertFirebaseUser(linkedUser)
          setUser(convertedUser)
          return convertedUser
        } catch (linkError: any) {
          // Handle credential already in use error
          if (linkError.code === AUTH_ERROR_CODES.CREDENTIAL_ALREADY_IN_USE ||
              linkError.code === AUTH_ERROR_CODES.EMAIL_ALREADY_IN_USE) {
            console.log('🔄 Email credential already in use, signing in with existing account...')

            // Sign in with the existing credential
            const result = await signInWithEmailAndPassword(auth, email, password)
            const existingUser = result.user

            console.log('✅ Signed in with existing account:', existingUser.uid)
            console.log('ℹ️ Skipping data migration - user already has existing account data')

            const convertedUser = convertFirebaseUser(existingUser)
            setUser(convertedUser)
            return convertedUser
          }
          // Re-throw other linking errors
          throw linkError
        }
      } else {
        // Regular email sign-in for non-anonymous users
        const result = await signInWithEmailAndPassword(auth, email, password)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign in error:', error)
      setError(error.message || 'Failed to sign in with email')
      return null
    }
  }

  const signUpWithEmail = async (email: string, password: string, displayName: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        try {
          // Create email credential and link
          const credential = EmailAuthProvider.credential(email, password)
          const result = await linkWithCredential(currentUser, credential)
          const linkedUser = result.user

          console.log('✅ Account linked successfully:', linkedUser.uid)

          // Update profile with display name after linking
          if (displayName) {
            await firebaseUpdateProfile(linkedUser, { displayName })
          }

          // Migrate anonymous user data to the linked account
          await handleDataMigration(anonymousUid, linkedUser.uid)

          const convertedUser = convertFirebaseUser(linkedUser)
          setUser(convertedUser)
          return convertedUser
        } catch (linkError: any) {
          // Handle credential already in use error
          if (linkError.code === AUTH_ERROR_CODES.CREDENTIAL_ALREADY_IN_USE ||
              linkError.code === AUTH_ERROR_CODES.EMAIL_ALREADY_IN_USE) {
            console.log('🔄 Email credential already in use, signing in with existing account...')

            // Sign in with the existing credential
            const result = await signInWithEmailAndPassword(auth, email, password)
            const existingUser = result.user

            console.log('✅ Signed in with existing account:', existingUser.uid)

            // Update profile with display name if provided
            if (displayName && !existingUser.displayName) {
              await firebaseUpdateProfile(existingUser, { displayName })
            }

            console.log('ℹ️ Skipping data migration - user already has existing account data')

            const convertedUser = convertFirebaseUser(existingUser)
            setUser(convertedUser)
            return convertedUser
          }
          // Re-throw other linking errors
          throw linkError
        }
      } else {
        // Regular email sign-up for non-anonymous users
        const result = await createUserWithEmailAndPassword(auth, email, password)

        // Update profile with display name
        if (displayName) {
          await firebaseUpdateProfile(result.user, { displayName })
        }

        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign up error:', error)
      setError(error.message || 'Failed to create account')
      return null
    }
  }

  const signInAnonymously = useCallback(async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      setIsCreatingUser(true)

      const result = await firebaseSignInAnonymously(auth)
      const convertedUser = convertFirebaseUser(result.user)

      setUser(convertedUser)
      setIsCreatingUser(false)
      return convertedUser
    } catch (error: any) {
      console.error('Anonymous sign in error:', error)
      setError(error.message || 'Failed to sign in anonymously')
      setIsCreatingUser(false)
      return null
    }
  }, [auth])

  const signOut = async (): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await firebaseSignOut(auth)
      setUser(null)
    } catch (error: any) {
      console.error('Sign out error:', error)
      setError(error.message || 'Failed to sign out')
    }
  }

  const resetPassword = async (email: string): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      console.error('Reset password error:', error)
      setError(error.message || 'Failed to send password reset email')
    }
  }

  const updateProfile = async (updates: { displayName?: string; photoURL?: string }): Promise<void> => {
    if (!auth?.currentUser) {
      setError('No authenticated user')
      return
    }

    try {
      setError(null)
      await firebaseUpdateProfile(auth.currentUser, updates)

      // Update local user state
      if (user) {
        setUser({
          ...user,
          displayName: updates.displayName || user.displayName,
          photoURL: updates.photoURL || user.photoURL,
        })
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      setError(error.message || 'Failed to update profile')
    }
  }

  const deleteAccount = async (): Promise<void> => {
    if (!auth?.currentUser || !user) {
      setError('No authenticated user')
      return
    }

    // Don't allow deletion of anonymous accounts
    if (user.isAnonymous) {
      setError('Cannot delete anonymous accounts')
      return
    }

    try {
      setError(null)
      const userId = user.uid
      const currentUser = auth.currentUser

      console.log('🗑️ Starting account deletion process for user:', userId)

      // Validate account deletion eligibility
      const validation = firebaseAccountService.validateAccountDeletion(currentUser)
      if (!validation.canDelete) {
        throw new Error(validation.reason || 'Account deletion not allowed')
      }

      // Use the dedicated account service for deletion
      await firebaseAccountService.deleteUserAccount(userId, currentUser)

      console.log('✅ Account deletion completed successfully')

      // Clear local state
      setUser(null)

      // The auth state change will trigger anonymous sign-in automatically
      // which will redirect the user to the search view
    } catch (error: any) {
      console.error('❌ Account deletion error:', error)

      // The account service already provides user-friendly error messages
      const errorMessage = error.message || 'Failed to delete account. Please try again.'

      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }



  useEffect(() => {
    if (!isInitialized) {
      return
    }

    // If Firebase is not configured, stop loading immediately
    if (!auth) {
      console.log('⚠️ Firebase not configured - proceeding without authentication')
      setIsLoading(false)
      setIsCreatingUser(false)
      return
    }

    let retryTimeoutId: NodeJS.Timeout | null = null

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const convertedUser = convertFirebaseUser(firebaseUser)
        setUser(convertedUser)
        setIsCreatingUser(false)
        setIsLoading(false)
      } else {
        // Only try anonymous sign-in if Firebase is properly configured
        const hasValidConfig = process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
                              process.env.NEXT_PUBLIC_FIREBASE_API_KEY.trim() !== '' &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID.trim() !== ''

        if (hasValidConfig) {
          try {
            await signInAnonymously()
          } catch (error) {
            console.error('Failed to sign in anonymously:', error)

            // Quick retry with shorter delay
            retryTimeoutId = setTimeout(async () => {
              try {
                await signInAnonymously()
              } catch (retryError) {
                console.error('Retry failed:', retryError)
                setIsCreatingUser(false)
                setIsLoading(false)
              }
            }, 300)
          }
        } else {
          console.log('⚠️ Firebase not configured, skipping anonymous auth')
          setIsCreatingUser(false)
          setIsLoading(false)
        }
      }
    })

    return () => {
      unsubscribe()
      if (retryTimeoutId) {
        clearTimeout(retryTimeoutId)
      }
    }
  }, [auth, isInitialized, signInAnonymously])

  const value = {
    user,
    isAuthenticated,
    isLoading,
    isCreatingUser,
    isUserReady,
    error,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    signOut,
    resetPassword,
    updateProfile,
    deleteAccount,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
