'use client'

import Image from 'next/image'
import { QueueItem as QueueItemType } from '@/lib/types/queue'
import { useQueue } from '@/hooks/useTublist'
import { formatDuration } from '@/lib/utils/format'
import { formatTime } from '@/lib/utils/time'
import { useI18n } from '@/hooks/useI18n'

interface QueueItemProps {
  item: QueueItemType
  index: number
  isActive?: boolean
  onClick?: () => void
}

export function TublistItem({ item, index, isActive = false, onClick }: QueueItemProps) {
  const { playVideo } = useQueue()
  const { t } = useI18n()

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      playVideo(index)
    }
  }

  return (
    <div
      className={`queue-item cursor-pointer ${
        isActive ? 'bg-primary-600/20 border-primary-500/50' : ''
      }`}
      onClick={handleClick}
    >
      {/* Thumbnail */}
      <div className="relative flex-shrink-0">
        <Image
          src={item.thumbnail}
          alt={item.title}
          width={80}
          height={60}
          className="rounded-lg object-cover"
          style={{ width: '80px', height: '60px' }}
        />
        {item.duration && (
          <span className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
            {formatDuration(item.duration)}
          </span>
        )}
        {isActive && (
          <div className="absolute inset-0 bg-primary-600/30 rounded-lg flex items-center justify-center">
            <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className={`font-medium truncate ${isActive ? 'text-primary-300' : 'text-white'}`}>
          {item.title}
        </h4>
        <p className="text-sm text-dark-300 truncate">
          {item.channel || t('messages.unknownChannel')}
        </p>
        {item.viewCount && (
          <p className="text-xs text-dark-400">
            {item.viewCount.toLocaleString()} {t('labels.views')}
          </p>
        )}

        {/* Loop and timeframe information */}
        {(item.timeframes.length > 0 || item.loopSettings.videoLoopCount > 1) && (
          <div className="flex gap-1 mt-1">
            {/* Timeframes badge */}
            {item.timeframes.length > 0 && (
              <span className="bg-primary-600/20 text-primary-300 text-xs px-1.5 py-0.5 rounded flex items-center gap-1">
                <svg width="10" height="10" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                {item.timeframes.length} {item.timeframes.length > 1 ? t('labels.timeframes') : t('labels.timeframe')}
              </span>
            )}

            {/* Video loop count badge */}
            {item.loopSettings.videoLoopCount > 1 && (
              <span className="bg-accent-600/20 text-accent-300 text-xs px-1.5 py-0.5 rounded flex items-center gap-1">
                <svg width="10" height="10" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                </svg>
                {item.loopSettings.videoLoopCount}x
              </span>
            )}

            {/* Loop mode badge */}
            {item.timeframes.length > 0 && (
              <span className="bg-blue-600/20 text-blue-300 text-xs px-1.5 py-0.5 rounded">
                {item.loopSettings.loopMode === 'timeframes-only' ? 'TF Only' : 'Full+TF'}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        {isActive && (
          <div className="text-primary-400">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
        )}
      </div>
    </div>
  )
}
