'use client'

import { useState } from 'react'
import { useI18n } from '@/hooks/useI18n'

export function FeedbackButton() {
  const { t } = useI18n()
  const [isHovered, setIsHovered] = useState(false)

  const handleFeedbackClick = () => {
    const subject = encodeURIComponent(t('feedback.emailSubject'))
    const body = encodeURIComponent(t('feedback.emailBody'))
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`
    
    // Open the user's default email client
    window.location.href = mailtoUrl
  }

  return (
    <button
      onClick={handleFeedbackClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`
        fixed bottom-6 right-6 z-50
        glassmorphism-strong
        flex items-center justify-center
        w-14 h-14 md:w-16 md:h-16
        rounded-full
        text-white
        transition-all duration-300 ease-in-out
        hover:scale-110 hover:shadow-lg hover:shadow-primary-500/25
        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900
        group
        ${isHovered ? 'bg-primary-600/30' : 'bg-white/10'}
      `}
      title={t('feedback.tooltip')}
      aria-label={t('feedback.tooltip')}
    >
      {/* Feedback Icon */}
      <svg 
        width="24" 
        height="24" 
        viewBox="0 0 24 24" 
        fill="currentColor"
        className={`
          transition-all duration-300
          ${isHovered ? 'text-primary-300 scale-110' : 'text-white'}
        `}
      >
        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 13.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm1-4.5h-2V6h2v5z"/>
      </svg>

      {/* Tooltip on hover - only visible on larger screens */}
      <div
        className={`
          absolute right-full mr-3 top-1/2 -translate-y-1/2
          hidden md:block
          px-3 py-2
          bg-dark-800 text-white text-sm font-medium
          rounded-lg border border-dark-600
          whitespace-nowrap
          transition-all duration-300
          ${isHovered 
            ? 'opacity-100 translate-x-0' 
            : 'opacity-0 translate-x-2 pointer-events-none'
          }
        `}
      >
        {t('feedback.button')}
        {/* Arrow pointing to button */}
        <div className="absolute left-full top-1/2 -translate-y-1/2 w-0 h-0 border-l-4 border-l-dark-800 border-y-4 border-y-transparent"></div>
      </div>

      {/* Pulse animation ring */}
      <div
        className={`
          absolute inset-0 rounded-full
          border-2 border-primary-400/50
          transition-all duration-1000
          ${isHovered ? 'scale-125 opacity-0' : 'scale-100 opacity-100'}
        `}
      />
    </button>
  )
}
