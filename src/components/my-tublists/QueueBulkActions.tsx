'use client'

import { useState } from 'react'
import { Queue } from '@/lib/types/queue'
import { firebaseService } from '@/lib/services/firebase'
import { useAuth } from '@/hooks/useAuth'

interface QueueBulkActionsProps {
  selectedQueues: Queue[]
  onSelectionChange: (queues: Queue[]) => void
  onBulkActionComplete: () => void
  allQueues: Queue[]
}

export function QueueBulkActions({ 
  selectedQueues, 
  onSelectionChange, 
  onBulkActionComplete,
  allQueues 
}: QueueBulkActionsProps) {
  const { user } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [showBulkMenu, setShowBulkMenu] = useState(false)

  const handleSelectAll = () => {
    if (selectedQueues.length === allQueues.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(allQueues)
    }
  }

  const handleBulkDelete = async () => {
    if (!user || isProcessing || selectedQueues.length === 0) return

    const confirmMessage = `Are you sure you want to delete ${selectedQueues.length} queue${selectedQueues.length > 1 ? 's' : ''}? This action cannot be undone.`
    if (!confirm(confirmMessage)) return

    setIsProcessing(true)
    try {
      const deletePromises = selectedQueues.map(queue =>
        firebaseService.deleteQueue(queue.id, user!.uid)
      )

      const results = await Promise.allSettled(deletePromises)
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failureCount = results.length - successCount

      console.log(`✅ Bulk delete completed: ${successCount} deleted, ${failureCount} failed`)
      
      onSelectionChange([])
      onBulkActionComplete()
    } catch (error) {
      console.error('Error in bulk delete:', error)
    } finally {
      setIsProcessing(false)
      setShowBulkMenu(false)
    }
  }

  const handleBulkMakePublic = async () => {
    if (!user || isProcessing || selectedQueues.length === 0) return

    setIsProcessing(true)
    try {
      const updatePromises = selectedQueues
        .filter(queue => !queue.isPublic)
        .map(queue =>
          firebaseService.updateQueue(queue.id, { isPublic: true }, user.uid)
        )

      const results = await Promise.allSettled(updatePromises)
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failureCount = results.length - successCount

      console.log(`✅ Bulk make public completed: ${successCount} updated, ${failureCount} failed`)
      
      onSelectionChange([])
      onBulkActionComplete()
    } catch (error) {
      console.error('Error in bulk make public:', error)
    } finally {
      setIsProcessing(false)
      setShowBulkMenu(false)
    }
  }

  const handleBulkMakePrivate = async () => {
    if (!user || isProcessing || selectedQueues.length === 0) return

    setIsProcessing(true)
    try {
      const updatePromises = selectedQueues
        .filter(queue => queue.isPublic)
        .map(queue =>
          firebaseService.updateQueue(queue.id, { isPublic: false }, user.uid)
        )

      const results = await Promise.allSettled(updatePromises)
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failureCount = results.length - successCount

      console.log(`✅ Bulk make private completed: ${successCount} updated, ${failureCount} failed`)
      
      onSelectionChange([])
      onBulkActionComplete()
    } catch (error) {
      console.error('Error in bulk make private:', error)
    } finally {
      setIsProcessing(false)
      setShowBulkMenu(false)
    }
  }

  const handleBulkDuplicate = async () => {
    if (!user || isProcessing || selectedQueues.length === 0) return

    setIsProcessing(true)
    try {
      const duplicatePromises = selectedQueues.map(queue =>
        firebaseService.duplicateQueue(
          queue.id,
          user.uid,
          `${queue.metadata.title} (Copy)`
        )
      )

      const results = await Promise.allSettled(duplicatePromises)
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failureCount = results.length - successCount

      console.log(`✅ Bulk duplicate completed: ${successCount} duplicated, ${failureCount} failed`)
      
      onSelectionChange([])
      onBulkActionComplete()
    } catch (error) {
      console.error('Error in bulk duplicate:', error)
    } finally {
      setIsProcessing(false)
      setShowBulkMenu(false)
    }
  }

  if (allQueues.length === 0) return null

  return (
    <div className="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg border border-white/10">
      {/* Selection Controls */}
      <div className="flex items-center gap-4">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={selectedQueues.length === allQueues.length && allQueues.length > 0}
            onChange={handleSelectAll}
            className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
          />
          <span className="text-sm text-dark-300">
            Select All ({selectedQueues.length}/{allQueues.length})
          </span>
        </label>

        {selectedQueues.length > 0 && (
          <span className="text-sm text-primary-400">
            {selectedQueues.length} queue{selectedQueues.length > 1 ? 's' : ''} selected
          </span>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedQueues.length > 0 && (
        <div className="relative">
          <button
            onClick={() => setShowBulkMenu(!showBulkMenu)}
            disabled={isProcessing}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50"
          >
            {isProcessing ? (
              <>
                <div className="loading-spinner w-4 h-4"></div>
                Processing...
              </>
            ) : (
              <>
                Bulk Actions
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7 10l5 5 5-5z"/>
                </svg>
              </>
            )}
          </button>

          {/* Bulk Actions Menu */}
          {showBulkMenu && !isProcessing && (
            <div className="absolute right-0 top-full mt-2 w-48 bg-dark-800 border border-white/10 rounded-lg shadow-xl z-50">
              <div className="p-2">
                <button
                  onClick={handleBulkDuplicate}
                  className="w-full text-left px-3 py-2 text-sm text-white hover:bg-dark-700 rounded-lg transition-colors duration-200"
                >
                  Duplicate Selected
                </button>
                <button
                  onClick={handleBulkMakePublic}
                  className="w-full text-left px-3 py-2 text-sm text-white hover:bg-dark-700 rounded-lg transition-colors duration-200"
                >
                  Make Public
                </button>
                <button
                  onClick={handleBulkMakePrivate}
                  className="w-full text-left px-3 py-2 text-sm text-white hover:bg-dark-700 rounded-lg transition-colors duration-200"
                >
                  Make Private
                </button>
                <hr className="my-2 border-white/10" />
                <button
                  onClick={handleBulkDelete}
                  className="w-full text-left px-3 py-2 text-sm text-red-400 hover:bg-red-400/10 rounded-lg transition-colors duration-200"
                >
                  Delete Selected
                </button>
              </div>
            </div>
          )}

          {/* Backdrop to close menu */}
          {showBulkMenu && (
            <div
              className="fixed inset-0 z-40"
              onClick={() => setShowBulkMenu(false)}
            />
          )}
        </div>
      )}
    </div>
  )
}
