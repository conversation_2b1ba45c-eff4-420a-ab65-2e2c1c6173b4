'use client'

import { Queue } from '@/lib/types/queue'
import { formatDuration } from '@/lib/utils/format'
import { useI18n } from '@/hooks/useI18n'

interface QueueStatsProps {
  queues: Queue[]
}

export function QueueStats({ queues }: QueueStatsProps) {
  const { t } = useI18n()
  const totalQueues = queues.length
  const publicQueues = queues.filter(q => q.isPublic).length
  const privateQueues = totalQueues - publicQueues
  
  const totalVideos = queues.reduce((total, queue) => 
    total + (queue.metadata.videoCount || queue.queueData.items.length), 0
  )
  
  const totalDuration = queues.reduce((total, queue) => 
    total + (queue.metadata.totalDuration || 0), 0
  )
  
  const averageQueueLength = totalQueues > 0 ? Math.round(totalVideos / totalQueues) : 0
  
  const totalViews = queues.reduce((total, queue) => 
    total + (queue.metadata.viewCount || 0), 0
  )

  const recentQueues = queues
    .filter(queue => {
      const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
      return queue.createdAt > weekAgo
    })
    .length

  if (totalQueues === 0) {
    return null
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Queue Statistics</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Total Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-primary-400 mb-1">
            {totalQueues}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.totalQueues')}</div>
        </div>

        {/* Total Videos */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-blue-400 mb-1">
            {totalVideos}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.totalVideos')}</div>
        </div>

        {/* Total Duration */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-green-400 mb-1">
            {formatDuration(totalDuration)}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.totalDuration')}</div>
        </div>

        {/* Total Views */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-2xl font-bold text-purple-400 mb-1">
            {totalViews}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.totalViews')}</div>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        {/* Public Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-green-400 mb-1">
            {publicQueues}
          </div>
          <div className="text-sm text-dark-300">{t('labels.public')}</div>
        </div>

        {/* Private Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-gray-400 mb-1">
            {privateQueues}
          </div>
          <div className="text-sm text-dark-300">{t('labels.private')}</div>
        </div>

        {/* Average Length */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-yellow-400 mb-1">
            {averageQueueLength}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.averageQueueLength')}</div>
        </div>

        {/* Recent Queues */}
        <div className="text-center p-4 bg-dark-800/50 rounded-lg">
          <div className="text-xl font-bold text-orange-400 mb-1">
            {recentQueues}
          </div>
          <div className="text-sm text-dark-300">{t('statistics.thisWeek')}</div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="mt-4 p-4 bg-dark-800/30 rounded-lg">
        <h4 className="text-sm font-medium text-white mb-2">{t('statistics.quickInsights')}</h4>
        <div className="space-y-1 text-sm text-dark-300">
          {totalQueues > 0 && (
            <div>
              • {t('statistics.youHaveQueues', {
                count: totalQueues,
                plural: totalQueues > 1 ? 's' : '',
                videos: totalVideos,
                videoPlural: totalVideos > 1 ? 's' : ''
              })}
            </div>
          )}
          {publicQueues > 0 && (
            <div>
              • {t('statistics.publicQueuesShareable', { count: publicQueues })}
            </div>
          )}
          {totalDuration > 0 && (
            <div>
              • {t('statistics.totalWatchTime', { duration: formatDuration(totalDuration) })}
            </div>
          )}
          {recentQueues > 0 && (
            <div>
              • {t('statistics.createdThisWeek', {
                count: recentQueues,
                plural: recentQueues > 1 ? 's' : ''
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
