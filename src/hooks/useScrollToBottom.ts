'use client'

import { useState, useEffect, useCallback } from 'react'

interface UseScrollToBottomOptions {
  threshold?: number // Distance from bottom in pixels to trigger show
  debounceMs?: number // Debounce scroll events
}

export function useScrollToBottom(options: UseScrollToBottomOptions = {}) {
  const { threshold = 200, debounceMs = 100 } = options
  const [isNearBottom, setIsNearBottom] = useState(false)

  const checkScrollPosition = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight

    // Calculate if we're near the bottom
    const distanceFromBottom = documentHeight - (scrollTop + windowHeight)
    const nearBottom = distanceFromBottom <= threshold

    setIsNearBottom(nearBottom)
  }, [threshold])

  // Debounce function
  const debounce = useCallback((func: () => void, wait: number) => {
    let timeout: NodeJS.Timeout | null = null
    
    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(func, wait)
    }
  }, [])

  const debouncedCheckScroll = useCallback(
    debounce(checkScrollPosition, debounceMs),
    [checkScrollPosition, debounceMs]
  )

  useEffect(() => {
    // Check initial position
    checkScrollPosition()

    // Add scroll listener
    window.addEventListener('scroll', debouncedCheckScroll, { passive: true })
    window.addEventListener('resize', checkScrollPosition, { passive: true })

    return () => {
      window.removeEventListener('scroll', debouncedCheckScroll)
      window.removeEventListener('resize', checkScrollPosition)
    }
  }, [checkScrollPosition, debouncedCheckScroll])

  return isNearBottom
}
