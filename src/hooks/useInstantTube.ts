'use client'

import { createContext, useContext } from 'react'
import { VideoMetadata } from '@/lib/types/video'

export interface QuickPlayState {
  currentVideo: VideoMetadata | null
  isPlaying: boolean
  isLooping: boolean
  volume: number
  isLoading: boolean
  error: string | null
}

export interface QuickPlayContextType extends QuickPlayState {
  // Video management
  loadVideo: (url: string) => Promise<void>
  clearVideo: () => void
  
  // Playback controls
  play: () => void
  pause: () => void
  setVolume: (volume: number) => void
  toggleLoop: () => void
  
  // State setters (for internal use)
  setPlaying: (isPlaying: boolean) => void
  setLoading: (isLoading: boolean) => void
  setError: (error: string | null) => void
}

export const QuickPlayContext = createContext<QuickPlayContextType>({
  currentVideo: null,
  isPlaying: false,
  isLooping: true, // Default to infinite loop for quick play
  volume: 1,
  isLoading: false,
  error: null,
  
  loadVideo: async () => {},
  clearVideo: () => {},
  
  play: () => {},
  pause: () => {},
  setVolume: () => {},
  toggleLoop: () => {},
  
  setPlaying: () => {},
  setLoading: () => {},
  setError: () => {},
})

export const useQuickPlay = () => {
  const context = useContext(QuickPlayContext)
  if (!context) {
    throw new Error('useQuickPlay must be used within a QuickPlayProvider')
  }
  return context
}
