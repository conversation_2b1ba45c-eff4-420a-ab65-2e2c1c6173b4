'use client'

import { createContext, useContext } from 'react'
import { QueueState, QueueItem } from '@/lib/types/queue'
import { VideoMetadata } from '@/lib/types/video'

export interface QueueContextType extends QueueState {
  // Queue management
  addVideo: (video: VideoMetadata) => void
  removeVideo: (index: number) => void
  moveVideo: (fromIndex: number, toIndex: number) => void
  clearQueue: () => void
  loadQueue: (queue: QueueState) => void
  
  // Playback controls
  playVideo: (index?: number) => void
  pauseVideo: () => void
  nextVideo: () => void
  previousVideo: () => void
  seekTo: (seconds: number) => void
  setVolume: (volume: number) => void
  
  // Queue state
  setCurrentIndex: (index: number) => void
  setPlaying: (isPlaying: boolean) => void
  setQueueLoopCount: (queueLoopCount: number) => void
  restartQueue: (loopCount?: number) => void
  
  // Computed properties
  currentVideo: QueueItem | null
  hasNext: boolean
  hasPrevious: boolean
  queueDuration: number
  
  // Queue operations
  saveQueue: (title: string, isPublic?: boolean) => Promise<string | null>
  shareCurrentQueue: (title: string) => Promise<string | null>
}

export const TublistContext = createContext<QueueContextType>({
  items: [],
  currentIndex: 0,
  isPlaying: false,
  queueLoopCount: -1,
  volume: 1,
  timestamp: Date.now(),
  
  addVideo: () => {},
  removeVideo: () => {},
  moveVideo: () => {},
  clearQueue: () => {},
  loadQueue: () => {},
  
  playVideo: () => {},
  pauseVideo: () => {},
  nextVideo: () => {},
  previousVideo: () => {},
  seekTo: () => {},
  setVolume: () => {},
  
  setCurrentIndex: () => {},
  setPlaying: () => {},
  setQueueLoopCount: () => {},
  restartQueue: () => {},
  
  currentVideo: null,
  hasNext: false,
  hasPrevious: false,
  queueDuration: 0,
  
  saveQueue: async () => null,
  shareCurrentQueue: async () => null,
})

export const useTublist = () => {
  const context = useContext(TublistContext)
  if (!context) {
    throw new Error('useTublist must be used within a TublistProvider')
  }
  return context
}

// Keep the old useQueue hook for backward compatibility during transition
export const QueueContext = TublistContext
export const useQueue = useTublist
